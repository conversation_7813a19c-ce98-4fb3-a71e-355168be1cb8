from fastapi import FastAP<PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from loguru import logger
from api.routers import router

#  引入 opentelemetry 相关依赖
from mi_otel_python import exporter

# 根据需要选择对应框架的 opentelemetry 依赖
# 参考文档 https://xiaomi.f.mioffice.cn/docx/doxk4JtZZuZVfUrKSdb8PuPsGme?from=from_copylink
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor

from util.common_util import get_env_by_key
from config.logging_config import configure_logging
from util.mysql_db_manager import DBManager
from util.redis_manager import RedisManager
from config.run_config import (
    RUN_CONFIG_DICT,
    DATA_BASE_HOST,
    DATA_BASE_PORT,
    DATA_BASE_USER,
    DATA_BASE_PASSWORD,
    DATA_BASE_NAME,
    REDIS_HOST,
    REDIS_PORT,
    REDIS_PASSWORD
)

app = FastAPI(
    title="global_copilot_inference", description="global copilot inference, chat", version="1.0.0"
)

# 配置loguru（替换为从config导入）
configure_logging()  # 调用配置函数


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    error_details = []
    for error in exc.errors():
        field = "->".join(map(str, error["loc"]))
        error_details.append({"field": field, "type": error["type"], "msg": error["msg"]})

    # 记录到自定义日志系统
    logger.error(f"收到非法的输入:{error_details}")

    return JSONResponse(
        status_code=422,
        content={"detail": error_details},
    )


app.include_router(router, prefix="/api/v1")


ENV_NAME = get_env_by_key("ENV_NAME", "local")
logger.info(f"当前环境={ENV_NAME}")
if ENV_NAME != "local":
    access_key = get_env_by_key("ACCESS_KEY")
    access_secret = get_env_by_key("ACCESS_SECRET")
    # app初始化之后，初始化 opentelemetry
    # 测试时需要传入 traceDebug=True，否则使用默认值
    exporter.init(access_key, access_secret, trace_debug=False)
    RequestsInstrumentor().instrument()
    FastAPIInstrumentor().instrument_app(app)

    @app.on_event("startup")
    async def startup_event():
        """应用启动时初始化 DBManager"""
        try:
            # 初始化 DBManager 实例
            db_manager = DBManager(
                host=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_HOST],
                user=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_USER],
                password=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_PASSWORD],
                database=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_NAME],
                port=int(RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_PORT])
            )
            # 初始化 RedisManager 实例
            redis_manager = RedisManager(
                host=RUN_CONFIG_DICT[ENV_NAME][REDIS_HOST],
                port=int(RUN_CONFIG_DICT[ENV_NAME][REDIS_PORT]),
                password=RUN_CONFIG_DICT[ENV_NAME][REDIS_PASSWORD]
            )
            await redis_manager.connect()
        except Exception as e:
            logger.error(f"Failed to initialize DBManager or RedisManager: {e}")
            raise e  # FastAPI 将会在启动时终止应用

    @app.on_event("shutdown")
    async def shutdown_event():
        """应用关闭时断开 DBManager"""
        try:
            db_manager = DBManager(
                host=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_HOST],
                user=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_USER],
                password=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_PASSWORD],
                database=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_NAME],
                port=int(RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_PORT])
            )
            db_manager.disconnect()
            # 初始化 RedisManager 实例
            redis_manager = RedisManager(
                host=RUN_CONFIG_DICT[ENV_NAME][REDIS_HOST],
                port=int(RUN_CONFIG_DICT[ENV_NAME][REDIS_PORT]),
                password=RUN_CONFIG_DICT[ENV_NAME][REDIS_PASSWORD]
            )
            await redis_manager.disconnect()
        except Exception as e:
            logger.error(f"Failed to disconnect DBManager or RedisManager: {e}")

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=9001)
