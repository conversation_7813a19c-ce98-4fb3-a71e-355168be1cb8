from core.enum.message_type import MessageType
from core.enum.language import Language

# 拒答消息配置
REFUSAL_MESSAGE_DICT = {
    # 对不起，我当前只能回答和手机相关（主要是参数配置）的问题
    MessageType.NOT_IN_SCOPE: {
        Language.CHINESE: "抱歉，我目前只能回答与手机相关（主要是参数配置）的问题。",
        Language.ENGLISH: "Sorry, I can only answer questions related to phones (especially parameter configuration) at this time.",
        Language.INDONESIAN: "Maaf, saya hanya bisa menjawab pertanyaan terkait ponsel (terutama konfigurasi parameter) saat ini.",
    },
    # 问题中的机型与您选择的不一致，请检查后再提问
    MessageType.NOT_MATCH_ITEM: {
        Language.CHINESE: "所询问的型号与您选择的不一致，请检查后再询问。",
        Language.ENGLISH: "The model in question does not match your choice, please check before asking.",
        Language.INDONESIAN: "Model dalam pertanyaan tidak sesuai dengan pilihan <PERSON>, silakan periksa sebelum bertanya.",
    },
    # 对不起，我没有关于当前问题的知识，无法进行解答
    MessageType.NO_KNOWLEDGE: {
        Language.CHINESE: "对不起，我没有关于当前问题的知识，无法进行解答",
        Language.ENGLISH: "Sorry, I have no knowledge of the current issue and cannot answer it.",
        Language.INDONESIAN: "Maaf, saya tidak memiliki pengetahuan tentang masalah saat ini dan tidak dapat menjawabnya.",
    },
    # 产品售价、促销优惠、售后服务等信息请以当地小米官方信息为准
    MessageType.DYNAMIC_CHANGE: {
        Language.CHINESE: "产品售价、促销优惠、售后服务等信息请以当地小米官方信息为准",
        Language.ENGLISH: "Product prices, promotions, after-sales service and other information please refer to local Xiaomi official information.",
        Language.INDONESIAN: "Harga produk, promosi, layanan purna jual, dan informasi lainnya harap merujuk pada informasi resmi Xiaomi setempat.",
    },
    # 对不起，暂时还不支持您提到的这个型号
    MessageType.NOT_SUPPORTED_ITEM: {
        Language.CHINESE: "对不起，暂时还不支持您提到的这个型号",
        Language.ENGLISH: "Sorry, the model you mentioned is not supported yet.",
        Language.INDONESIAN: "Maaf, model yang Anda sebutkan belum didukung.",
    },
    # 对不起，您当前提问的对比机型数量少于2个，无法进行对比
    MessageType.ITEM_COMPARE_UNCERTAIN: {
        Language.CHINESE: "对不起，您当前提问的对比机型数量少于2个，无法进行对比",
        Language.ENGLISH: "Sorry, the number of comparison models you requested is less than 2, so a comparison cannot be performed.",
        Language.INDONESIAN: "Maaf, jumlah model perbandingan yang Anda minta kurang dari 2, jadi perbandingan tidak dapat dilakukan.",
    },
    # 对不起，我没有识别到您提问中的机型，请您重新提问
    MessageType.ITEM_UNRECOGNIZED: {
        Language.CHINESE: "对不起，我没有识别到您提问中的机型，请您重新提问",
        Language.ENGLISH: "Sorry, I don't recognize the model you asked about. Please ask again.",
        Language.INDONESIAN: "Maaf, saya tidak mengenali model yang Anda tanyakan. Silakan bertanya lagi.",
    },
    # 对不起，暂不支持非 3c 数码产品的问答
    MessageType.FREE_FAQ_REJECT: {
        Language.CHINESE: "对不起，暂不支持非 3c 数码产品的问答",
        Language.ENGLISH: "Sorry, we do not yet support inquiries about non-3c digital products.",
        Language.INDONESIAN: "Maaf, kami belum mendukung pertanyaan tentang produk digital non-3c.",
    },
    # 对不起，暂不支持非小米机型之间的对比
    MessageType.NO_XIAOMI_ITEM: {
        Language.CHINESE: "对不起，暂不支持非小米机型之间的对比",
        Language.ENGLISH: "Sorry, comparison between non-Xiaomi models is not supported yet.",
        Language.INDONESIAN: "Maaf, perbandingan antara model non-Xiaomi belum didukung.",
    },
    # 请跳转到两款设备对比页面，查看两款产品的区别
    MessageType.ITEM_COMPARE: {
        Language.CHINESE: "请跳转到两款设备对比页面，查看两款产品的区别",
        Language.ENGLISH: "Please head over to the two devices comparison page to see the differences between the two products.",
        Language.INDONESIAN: "Silakan beralih ke halaman perbandingan dua perangkat untuk melihat perbedaan antara kedua produk.",
    },
    # 您想询问以下哪一款产品
    MessageType.ITEM_CANDIDATE: {
        Language.CHINESE: "您想询问以下哪一款产品",
        Language.ENGLISH: "Which product do you want to ask about?",
        Language.INDONESIAN: "Anda ingin menanyakan produk yang mana?",
    }
}

# 其他聊天相关的配置项可以在这里添加
CANDIDATE_ITEM_SIZE = 5  # 候选机型数量 

# 参数问答预置回复语
PRE_MESSAGE_DICT = {
    Language.INDONESIAN: {
        "NO_ITEM_NAME": ["Oke, biarkan saya membantu Anda dengan itu.",
                         "Mohon tunggu, kami sedang mencari apa yang Anda butuhkan.",
                         "Oke, saya akan segera memeriksa informasi yang relevan untuk Anda.",
                         "Dipahami. Kami sedang mengambil informasi yang relevan untuk Anda."],
        "NO_SECOND_TAG": ["Oke, sekarang saya akan menjawab pertanyaan Anda tentang {}.",
                          "Mengambil informasi yang relevan tentang produk {}.",
                          "Mempersiapkan informasi rinci model {}.",
                          "Oke, saya akan menjawab pertanyaan Anda tentang produk {}."],
        "DEFAULT": ["Oke, izinkan saya membantu Anda menjawab pertanyaan Anda tentang aspek {} dari {}.",
                    "Oke, biar aku jelaskan secara detail  aspek {} dari {}.",
                    "Harap tunggu, kami sedang mengambil informasi produk untuk referensi Anda  aspek {} dari {}.",
                    "Diterima, saya akan memverifikasi informasi spesifik untuk Anda  aspek {} dari {}."]
    },
    Language.ENGLISH: {
        "NO_ITEM_NAME": ["Ok, let me help you.", "Please wait, we are looking for what you need.",
                         "Okay, I will check the relevant information for you right away.",
                         "Understood. We are retrieving relevant information for you."],
        "NO_SECOND_TAG": ["Okay, now I will answer your questions about {}.",
                          "Retrieving relevant information about product {}.",
                          "Preparing detailed information of model {}.",
                          "Ok, I will answer your questions about product {}."],
        "DEFAULT": ["Ok, let me help you answer your question about {} aspect of {}.",
                    "Okay, let me explain in detail about {} aspect of {}.",
                    "Please wait, we are retrieving product information for your reference about {} aspect of {}.",
                    "Accepted, I will verify specific information for you about {} aspect of {}."]
    },
    Language.CHINESE: {
        "NO_ITEM_NAME": ["好的，让我来帮助你。", "请稍候，我们正在寻找您需要的内容。", "好的，我马上帮您查一下相关信息。",
                         "我们正在为您检索相关信息。"],
        "NO_SECOND_TAG": ["好的，现在我将回答您关于 {} 的问题。", "正在检索有关产品 {} 的相关信息。",
                          "正在准备产品 {} 的详细信息。", "好的，我会回答您关于产品 {} 的问题"],
        "DEFAULT": ["好的，让我来帮您解答关于{}的{}方面的问题。", "好的，让我详细解释一下{}的{}方面。",
                    "请稍候，我们正在检索有关{}的{}方面的产品信息，供您参考。",
                    "收到，我将为您核实有关{}的{}方面的具体信息。"]
    }
}

MESSAGE_PROMPT_PREFIX = {
    MessageType.ITEM_CONFIRM: {

    }
}
