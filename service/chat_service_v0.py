import asyncio
import time
from asyncio import TaskGroup

from fastapi import Request
from core.schema.chat_request import ChatRequest
from core.schema.chat_response import EventType
from core.enum.message_type import MessageType
from core.schema.constant import CHAT
from service.chat_service_base import ChatServiceBase
from util.common_util import is_empty, check_cancellation


class ChatServiceV0(ChatServiceBase):
    """430版本的聊天服务"""

    async def chat_inner(self, chat_request: ChatRequest, http_request: Request):
        chat_request.logger.debug(f"item name: {chat_request.item_name}")
        if chat_request.version is None or chat_request.version == 0:
            # 处理 430 单轮对话
            chat_request.logger.debug(f"执行 430 版问答")
            self._counter.labels(object=CHAT, condition="mvp").inc()
            async for response in self.chat_branch_430(chat_request, http_request):
                yield response
            return

        chat_request.logger.error(f"ChatServiceV0 不支持的 chat version {chat_request.version}")
        raise RuntimeError(f"ChatServiceV0 不支持的 chat version {chat_request.version}")

    async def chat_branch_430(self, chat_request: ChatRequest, http_request: Request):
        # 检查取消
        if check_cancellation(self._redis_manager, chat_request.request_id):
            chat_request.logger.info(f"请求 {chat_request.request_id} 被取消。")
            return
        # 如果数据中暂时不支持当前机型，直接拒答
        if chat_request.item_name_normalized not in self._normalized_item_name_list:
            chat_request.logger.error(
                f"接收到不支持的机型：{chat_request.item_name}({chat_request.item_name_normalized})")
            chat_request.logger.debug(f"当前支持的机型：{self._normalized_item_name_list}")
            # 返回拒答固定话术
            for response in self.refuse_answer(MessageType.NOT_SUPPORTED_ITEM, chat_request):
                yield response
            return

        async with TaskGroup() as tg:
            # init task
            self._task_filter = tg.create_task(
                self._query_parse_service.filter_by_first_tags_430(chat_request, tg),
                name="过滤任务",
            )
            # 只是一个异步生成器对象，而不会立即执行函数体里的代码
            answer_agent = self.generate_response_stream_430(chat_request, http_request, tg)
            # _task_answer 只要 yield 第一个 event 就算完成了
            self._task_answer_first_event = tg.create_task(
                answer_agent.__anext__(), name="回答任务"
            )
            done_tasks, pending_tasks = await asyncio.wait(
                {self._task_filter, self._task_answer_first_event},
                return_when=asyncio.FIRST_COMPLETED,
            )
            if self._task_filter in done_tasks:
                # 如果过滤任务先返回结果了：根据过滤结果决定是否继续回答
                answer_type, self._actual_item_name_list, self._first_tags, total_tokens_filter_by_first_tags = self._task_filter.result()
                chat_request.logger.info(f"过滤结果先返回了：{answer_type}, {self._first_tags}")
                if answer_type == MessageType.TEXT:
                    # 通过了，则等待 _task_answer 任务完成
                    self._counter.labels(object=CHAT, condition=MessageType.TEXT.name).inc()
                    first_response = await self._task_answer_first_event
                    yield first_response
                    # 此后，持续迭代剩余的事件
                    async for response in answer_agent:
                        if chat_request.debug and response.event == EventType.FINISH_EVENT:
                            response.data.total_tokens += total_tokens_filter_by_first_tags
                        yield response
                    return

                # 拒答，取消其他任务
                await self.cancel_tasks(chat_request,
                                        [
                                            self._task_answer_first_event,
                                            self._task_retrieval_doc,
                                            self._task_retrieval_faq,
                                            self._task_retrieval_minet,
                                        ]
                                        )
                # 返回拒答固定话术
                for response in self.refuse_answer(answer_type, chat_request):
                    if chat_request.debug and response.event == EventType.FINISH_EVENT:
                        response.data.total_tokens += total_tokens_filter_by_first_tags
                    yield response
                return

            if self._task_answer_first_event in done_tasks:
                # 如果回答任务先返回第一个 event
                chat_request.logger.info("回答先返回了第一个 event，继续等待过滤任务完成")
                await self._task_filter
                answer_type, self._actual_item_name_list, self._first_tags, total_tokens_filter_by_first_tags = self._task_filter.result()
                chat_request.logger.info(f"过滤结果在回答之后返回了：{answer_type}, {self._first_tags}")
                if answer_type == MessageType.TEXT:
                    self._counter.labels(object=CHAT, condition=MessageType.TEXT.name).inc()
                    # 开始回答
                    first_response = self._task_answer_first_event.result()
                    yield first_response
                    # 此后，持续迭代剩余的事件
                    async for response in answer_agent:
                        if chat_request.debug and response.event == EventType.FINISH_EVENT:
                            response.data.total_tokens += total_tokens_filter_by_first_tags
                        yield response
                    return

                # 拒答，取消其他任务
                await self.cancel_tasks(chat_request,
                                        [
                                            self._task_answer_first_event,
                                            self._task_retrieval_doc,
                                            self._task_retrieval_faq,
                                            self._task_retrieval_minet,
                                        ]
                                        )
                # 返回拒答固定话术
                for response in self.refuse_answer(answer_type, chat_request):
                    if chat_request.debug and response.event == EventType.FINISH_EVENT:
                        response.data.total_tokens += total_tokens_filter_by_first_tags
                    yield response
                return

    async def generate_response_stream_430(self, chat_request, http_request, task_group: TaskGroup):
        self.log_elapse("二级标签", chat_request)
        start_time = time.time()
        # ToDo(hm): from here 430 咋整？
        second_tags, total_tokens_recognize_tage, _ = await self._query_parse_service.recognize_tags(
            chat_request
        )
        self.record_execution_time('recognize_tags', start_time)
        self.record_token_usage('recognize_tags', total_tokens_recognize_tage)
        self._second_tags = second_tags
        second_tag_map_dict = self._query_parse_service.get_second_tag_map_dict_from_tag_list_and_language(second_tags, chat_request.language.get_chinese_name())
        chat_request.logger.debug(f"识别出的二级标签：{','.join(self._second_tags)}")
        start_time = time.time()
        retrieval_task_list = await self.execute_knowledge_retrieval(chat_request,
                                                                     chat_request.ending_message.content,
                                                                     second_tag_map_dict,
                                                                     chat_request.item_name_normalized, task_group
                                                                     )
        self.record_execution_time('execute_knowledge_retrieval', start_time)
        start_time = time.time()
        knowledge_dict = await self.get_retrieval_results(chat_request, retrieval_task_list)
        self.record_execution_time('get_retrieval_results', start_time)
        self.log_elapse("检索完知识，构建 prompt", chat_request)
        system_prompt, user_prompt = self._prompt_build_service.build_prompt(chat_request, knowledge_dict)
        if is_empty(knowledge_dict):
            for response in self.refuse_answer(MessageType.NO_KNOWLEDGE, chat_request):
                if chat_request.debug and response.event == EventType.FINISH_EVENT:
                    response.data.total_tokens = total_tokens_recognize_tage
                yield response
            return

        async for response in self.generate_response_stream(system_prompt, user_prompt, chat_request, http_request, MessageType.TEXT):
            yield response
