import time
import random
import <PERSON><PERSON><PERSON><PERSON>
from asyncio import TaskGroup

from fastapi import Request
from config.chat_config import REFUSAL_MESSAGE_DICT
from core.schema.chat_request import ChatRequest
from core.schema.chat_response import EventType, ChatResponse, ChatResponseData
from core.enum.intent_type import IntentType
from core.enum.message_type import MessageType
from core.schema.constant import CHAT
from service.chat_service_base import ChatServiceBase
from util.common_util import is_empty, check_cancellation
from core.processor import normalize_item_name


class ChatServiceV1(ChatServiceBase):

    async def chat_inner(self, chat_request: ChatRequest, http_request: Request):
        # 处理 530 版本
        chat_request.logger.debug(f"执行 530 版问答")
        self._counter.labels(object=CHAT, condition="530").inc()
        start_time = time.time()
        async for response in self.chat_branch_v1(chat_request, http_request):
            if response.event == EventType.FINISH_EVENT:
                self.record_execution_time('530_response_time', start_time)
            yield response
        return

    async def chat_branch_v1(self, chat_request: ChatRequest, http_request: Request):
        # 检查取消
        if check_cancellation(self._redis_manager, chat_request.request_id):
            chat_request.logger.info(f"请求 {chat_request.request_id} 被取消。")
            return
        if chat_request.ending_message.type == MessageType.ITEM_CONFIRM:
            # 如果本轮是用户确认了机型，还是要重新做一遍意图识别，因为之前提问的 message 的意图没有存储
            answer_intent, total_tokens_tag_question_first, start_time = await self._query_parse_service.tag_question_first(
                chat_request)
            self._answer_intent = answer_intent
            confirmed_item = chat_request.ending_message.item_list[0]
            chat_request.item_id = confirmed_item.item_id
            chat_request.item_name = confirmed_item.item_name
            chat_request.category_id = confirmed_item.category_id
            chat_request.category_name = confirmed_item.category_name
            chat_request.item_name_normalized = normalize_item_name(confirmed_item.item_name)
            async for response in self.single_round_chat(chat_request, http_request, answer_intent):
                yield response
            return

        chat_request.logger.debug(f"根据用户提问判断意图")
        # 判断意图, 意图分为问答类、双机对比、自由问答
        start_time = time.time()
        language, answer_intent, exact_item_names, fuzzy_item_names, extracted_item_names = await self.parse_question(
            chat_request)
        self._answer_intent = answer_intent
        self.record_execution_time('level_1_async', start_time)
        chat_request.logger.debug(f"意图识别: {answer_intent}")
        if answer_intent in IntentType.FREE_QUESTION_REJECT.as_set():
            # 自由问答
            chat_request.logger.debug(f"提问为: 3c数码产品无关的自由问答类意图")
            system_prompt, user_prompt = self._prompt_build_service.build_free_question_reject_prompt(chat_request,
                                                                                                      answer_intent)
            async for chat_response in self.generate_response_stream(system_prompt, user_prompt, chat_request,
                                                                     http_request, MessageType.FREE_FAQ_REJECT):
                yield chat_response
            return

        if answer_intent in IntentType.FREE_QUESTION.as_set():
            # 自由问答
            chat_request.logger.debug(f"提问为: 3c数码产品相关的自由问答类意图")
            system_prompt, user_prompt = self._prompt_build_service.build_free_question_prompt(chat_request)
            async for chat_response in self.generate_response_stream(system_prompt, user_prompt, chat_request,
                                                                     http_request, MessageType.FREE_FAQ_ANSWER):
                yield chat_response
            return

        if answer_intent in IntentType.ITEM_COMPARE.as_set():
            # 双机对比
            chat_request.logger.debug(f"提问为: 双机对比类意图")
            for response in self.item_compare_response_stream(chat_request, exact_item_names, extracted_item_names):
                yield response
            return

        # 问答类
        # 当前对话类型，目前有2种类型：1-文本 7-用户确认的机型
        chat_request.logger.debug(f"提问为: 问答类意图")
        async for response in self.question_answer_response_stream(chat_request, http_request,
                                                                   exact_item_names, fuzzy_item_names, answer_intent):
            yield response
        return

    # 单机问答
    async def single_round_chat(self, chat_request: ChatRequest, http_request: Request, answer_intent):
        # 如果数据中暂时不支持当前机型，直接拒答
        if chat_request.item_name_normalized not in self._normalized_item_name_list:
            chat_request.logger.error(f"接收到不支持的机型：{chat_request.item_name_normalized}")
            chat_request.logger.debug(f"当前支持的机型：{self._normalized_item_name_list}")
            # 返回拒答固定话术
            for response in self.refuse_answer(MessageType.NOT_SUPPORTED_ITEM, chat_request):
                yield response
            return

        # ToDo(hm): tg 可以放到外面
        async with TaskGroup() as tg:
            # init task
            self._counter.labels(object=CHAT, condition=MessageType.TEXT.name).inc()
            response_selected_item = self.get_item_by_name(chat_request.item_name)
            async for response in self.generate_answer_response_stream(chat_request, http_request, tg, answer_intent):
                # 更新吸顶机型
                response.data.selected_item = response_selected_item
                yield response
            return

    # 用户确认机型回答-530
    @staticmethod
    def user_selected_answer(content, updated_item_list, answer_type):
        chat_response = ChatResponse(
            event=EventType.START_EVENT,
            data=ChatResponseData(answer_type=answer_type, item_list=updated_item_list)
        )
        yield chat_response

        chat_response = ChatResponse(
            event=EventType.TEXT_CHUNK_EVENT,
            data=ChatResponseData(text=content, answer_type=answer_type, item_list=updated_item_list)
        )
        yield chat_response

        chat_response = ChatResponse(
            event=EventType.FINISH_EVENT,
            data=ChatResponseData(text=content, answer_type=answer_type, item_list=updated_item_list)
        )
        yield chat_response

    async def generate_answer_response_stream(self, chat_request, http_request, task_group: TaskGroup, answer_intent):
        if is_empty(chat_request.item_name):
            chat_request.logger.error("单机参数问答时没有传入 item_name")
        pre_thinking_str = self.get_pre_thinking_str(chat_request, self._second_tags_map_dict)
        # markdown 斜体
        # wrapped_pre_thinking_str = f"*{pre_thinking_str}*\n"
        # 前端自定义的引用格式
        wrapped_pre_thinking_str = f"%% {pre_thinking_str}\n"
        for chat_response in self.wrap_str_to_response_stream(wrapped_pre_thinking_str, answer_type=MessageType.TEXT,
                                                              need_finish_event=False):
            yield chat_response
        start_time = time.time()
        retrieval_task_list = await self.execute_knowledge_retrieval(chat_request,
                                                                     chat_request.rewritten_query,
                                                                     self._second_tags_map_dict,
                                                                     chat_request.item_name_normalized, task_group
                                                                     )
        self.record_execution_time('execute_knowledge_retrieval', start_time)
        start_time = time.time()
        knowledge_dict = await self.get_retrieval_results(chat_request, retrieval_task_list)
        self.record_execution_time('get_retrieval_results', start_time)
        self.log_elapse("检索完知识，构建 prompt", chat_request)
        system_prompt, user_prompt = self._prompt_build_service.build_prompt(chat_request, knowledge_dict,
                                                                             answer_intent)
        if is_empty(knowledge_dict):
            for response in self.refuse_answer(MessageType.NO_KNOWLEDGE, chat_request):
                yield response
            return

        async for response in self.generate_response_stream(system_prompt, user_prompt, chat_request, http_request,
                                                            MessageType.TEXT,
                                                            need_first_event=False,
                                                            pre_thinking_str=wrapped_pre_thinking_str):
            yield response

    def provide_candidate_items(self, xiaomi_candidates, query):
        candidates = []
        # 优先从xiaomi_candidates中选取
        for name in xiaomi_candidates:
            if len(candidates) < self._candidate_item_size:
                candidates.append(name)
            else:
                break
        if len(candidates) < self._candidate_item_size:
            other_candidates = random.sample(list(set(self._item_name_xiaomi_list) - set(candidates)),
                                             self._candidate_item_size - len(candidates))
            candidates.extend(other_candidates)
        candidates = list(set(candidates))
        candidates = sorted(candidates,
                            key=lambda item: 1.0 - Levenshtein.distance(query, item) / max(len(query), len(item)),
                            reverse=True)
        candidates = [self.get_item_by_name(name) for name in candidates]
        assert len(candidates) == self._candidate_item_size, f"候选机型数量不足 {self._candidate_item_size} 个"
        return candidates

    # 问答类意图响应-530
    async def question_answer_response_stream(self, chat_request: ChatRequest, http_request: Request,
                                              exact_item_names, fuzzy_item_names, answer_intent):
        language = chat_request.language
        if chat_request.recognize_language is not None:
            language = chat_request.recognize_language
        is_spu_fuzzy = False
        if len(set(fuzzy_item_names) - set(exact_item_names)) > 0:
            is_spu_fuzzy = True
        fuzzy_item_names_with_exact = list(set(exact_item_names + fuzzy_item_names))
        chat_request.logger.debug(f'fuzzy_item_names_with_exact: {fuzzy_item_names_with_exact}')

        # 分类机型名称为xiaomi和non_xiaomi
        xiaomi_exact, non_xiaomi_exact = self.categorize_item_names(exact_item_names)
        xiaomi_fuzzy_with_exact, non_xiaomi_fuzzy_with_exact = self.categorize_item_names(fuzzy_item_names_with_exact)
        if is_empty(chat_request.item_name):
            chat_request.logger.debug("无吸顶机型")
            if len(fuzzy_item_names_with_exact) <= 0:
                chat_request.logger.debug(f"未识别出query中的机型，走自由问答")
                # 返回自由问答话术
                system_prompt, user_prompt = self._prompt_build_service.build_free_question_prompt(chat_request)
                answer_type = MessageType.FREE_FAQ_ANSWER
                async for chat_response in self.generate_response_stream(system_prompt, user_prompt, chat_request,
                                                                         http_request, answer_type):
                    yield chat_response
                return

            if len(xiaomi_exact) <= 0:
                # 提供给用户候选机型
                candidates = self.provide_candidate_items(xiaomi_fuzzy_with_exact, chat_request.rewritten_query)
                content = REFUSAL_MESSAGE_DICT[MessageType.ITEM_CANDIDATE][language]
                for response in self.user_selected_answer(content, candidates,
                                                          answer_type=MessageType.ITEM_CANDIDATE):
                    yield response
                return

            # 直接更新吸顶机型
            chat_request.logger.debug("精确识别到xiaomi机型，更新吸顶机型。")
            # 选择一个xiaomi机型进行更新
            new_pinned_item = xiaomi_exact[0]
            chat_request.logger.debug(f"更新吸顶机型为: {new_pinned_item}")
            chat_request.logger.debug(f"识别出query中的确定性机型 || {new_pinned_item}，进行问答流程")
            # 如果数据中暂时不支持当前机型，直接拒答
            chat_request.item_name = new_pinned_item
            chat_request.item_name_normalized = normalize_item_name(new_pinned_item)
            async for response in self.single_round_chat(chat_request, http_request, answer_intent):
                yield response
            return

        if len(fuzzy_item_names_with_exact) <= 0:
            # 将原始吸顶机型作为更新后的机型
            chat_request.logger.debug("未识别出任何机型，使用原始的吸顶机型作为回答。")
            # 使用原始吸顶机型作为回答
            async for response in self.single_round_chat(chat_request, http_request, answer_intent):
                yield response
            return

        # 根据is_spu_fuzzy的值，判断是否模糊，如果是True（模糊），则从模糊列表中拿出5个机型作为候选，提供给用户，
        # 注意：需要优先从xiaomi列表中选，不足的，再从非xiaomi机型中选择；如果不模糊，则判断exact_item_names 精确识别机型的列表
        # 是否为空，如果是空，则把原始的吸顶机型作为更新后的机型，否则，判断吸顶机型是否在识别出的精确机型中，如果在，则保持原始的吸顶机型，
        # 否则，需要从精确识别的xiaomi机型中，拿出第一个机型作为更新后的吸顶机型
        if is_spu_fuzzy:
            chat_request.logger.debug("识别结果为模糊识别，提供候选机型。")
            # 提供候选机型（最多5个），优先从xiaomi列表中选
            candidates = self.provide_candidate_items(xiaomi_fuzzy_with_exact, chat_request.rewritten_query)
            content = REFUSAL_MESSAGE_DICT[MessageType.ITEM_CANDIDATE][language]
            for response in self.user_selected_answer(content, candidates,
                                                      answer_type=MessageType.ITEM_CANDIDATE):
                yield response
            return

        chat_request.logger.debug("识别结果为精确识别，处理精确机型。")
        if len(xiaomi_exact) <= 0:
            # 没有精确识别到xiaomi机型，保持原始吸顶机型
            async for response in self.single_round_chat(chat_request, http_request, answer_intent):
                yield response
            return

        if chat_request.item_name in xiaomi_exact:
            chat_request.logger.debug("吸顶机型在精确识别的机型列表中，保持原始吸顶机型。")
            # 保持原始吸顶机型
            async for response in self.single_round_chat(chat_request, http_request, answer_intent):
                yield response
            return

        # 更新吸顶机型为精确识别到的第一个xiaomi机型
        new_pinned_item = xiaomi_exact[0]
        chat_request.logger.debug(f"更新吸顶机型为: {new_pinned_item}")
        chat_request.logger.debug(f"识别出query中的确定性机型 || {new_pinned_item}，进行问答流程")
        chat_request.item_name = new_pinned_item
        chat_request.item_name_normalized = normalize_item_name(new_pinned_item)
        async for response in self.single_round_chat(chat_request, http_request, answer_intent):
            yield response

    # 双机对比意图响应
    def item_compare_response_stream(self, chat_request: ChatRequest, recognize_query_item_names, extracted_item_names):
        language = chat_request.language
        if chat_request.recognize_language is not None:
            language = chat_request.recognize_language
        # 分类机型名称为xiaomi和non_xiaomi
        item_names_xiaomi, item_names_non_xiaomi = self.categorize_item_names(recognize_query_item_names)
        not_in_candidates = self.get_item_name_not_in_candidates(extracted_item_names)
        chat_request.logger.debug(f"item_names_xiaomi: {item_names_xiaomi}")
        chat_request.logger.info(f"item_names_non_xiaomi: {item_names_non_xiaomi}")
        if is_empty(chat_request.item_name):
            chat_request.logger.debug("无吸顶机型，识别query中的机型。")
            # 识别query中的机型
            if len(item_names_xiaomi) + len(item_names_non_xiaomi) < 2:
                chat_request.logger.debug("识别到的机型少于2个，无法进行双机对比，拒答。")
                for response in self.refuse_answer(MessageType.ITEM_COMPARE_UNCERTAIN, chat_request):
                    if len(not_in_candidates) > 0 and (
                            response.event == EventType.FINISH_EVENT or response.event == EventType.TEXT_CHUNK_EVENT):
                        response.data.text = REFUSAL_MESSAGE_DICT[MessageType.ITEM_COMPARE][language]
                    yield response
                return

            # 确保至少有一个xiaomi机型
            if not item_names_xiaomi:
                chat_request.logger.debug("没有xiaomi机型可用于对比，拒答。")
                for response in self.refuse_answer(MessageType.NO_XIAOMI_ITEM, chat_request):
                    yield response
                return

            # 选择至少一个xiaomi机型
            updated_selected_item = item_names_xiaomi.pop(0)
            if item_names_xiaomi:
                second_item = item_names_xiaomi.pop(0)
            elif item_names_non_xiaomi:
                second_item = item_names_non_xiaomi.pop(0)
            else:
                chat_request.logger.debug("只有一个xiaomi机型且没有非xiaomi机型可用于对比，拒答。")
                for response in self.refuse_answer(MessageType.ITEM_COMPARE_UNCERTAIN, chat_request):
                    if len(not_in_candidates) > 0 and (
                            response.event == EventType.FINISH_EVENT or response.event == EventType.TEXT_CHUNK_EVENT):
                        response.data.text = REFUSAL_MESSAGE_DICT[MessageType.ITEM_COMPARE][language]
                    yield response
                return

            updated_item_list = [updated_selected_item, second_item]

        else:
            chat_request.logger.debug(f"当前吸顶机型: {chat_request.item_name}")
            total_recognized = len(item_names_xiaomi) + len(item_names_non_xiaomi)

            if total_recognized < 1:
                chat_request.logger.debug("没有足够的机型进行双机对比，拒答。")
                for response in self.refuse_answer(MessageType.ITEM_COMPARE_UNCERTAIN, chat_request):
                    if len(not_in_candidates) > 0 and (
                            response.event == EventType.FINISH_EVENT or response.event == EventType.TEXT_CHUNK_EVENT):
                        response.data.text = REFUSAL_MESSAGE_DICT[MessageType.ITEM_COMPARE][language]
                    yield response
                return

            if total_recognized == 1:
                # 只有一个识别到的机型，使用原始吸顶机型与该机型对比
                second_item = item_names_xiaomi.pop(0) if item_names_xiaomi else item_names_non_xiaomi.pop(0)
                updated_selected_item = chat_request.item_name
                updated_item_list = [updated_selected_item, second_item]
            else:
                # 识别到的机型数量大于1
                if item_names_xiaomi:
                    # 更新吸顶机型为识别到的第一个xiaomi机型
                    new_pinned_item = item_names_xiaomi.pop(0)
                    chat_request.logger.debug(f"更新吸顶机型为: {new_pinned_item}")
                    updated_selected_item = new_pinned_item
                    # 选择下一个机型进行对比
                    if item_names_xiaomi:
                        second_item = item_names_xiaomi.pop(0)
                    elif item_names_non_xiaomi:
                        second_item = item_names_non_xiaomi.pop(0)
                    else:
                        chat_request.logger.debug("没有足够的机型进行双机对比，拒答。")
                        for response in self.refuse_answer(MessageType.ITEM_COMPARE_UNCERTAIN, chat_request):
                            if len(not_in_candidates) > 0 and (
                                    response.event == EventType.FINISH_EVENT or response.event == EventType.TEXT_CHUNK_EVENT):
                                response.data.text = REFUSAL_MESSAGE_DICT[MessageType.ITEM_COMPARE][language]
                            yield response
                        return
                    updated_item_list = [updated_selected_item, second_item]
                else:
                    # 没有xiaomi机型，使用原始吸顶机型与一个非xiaomi机型对比
                    second_item = item_names_non_xiaomi.pop(0)
                    updated_selected_item = chat_request.item_name
                    updated_item_list = [updated_selected_item, second_item]

        chat_request.logger.debug(f"updated_selected_item: {updated_selected_item}")
        chat_request.logger.debug(f"updated_item_list: {updated_item_list}")
        updated_selected_item = self.get_item_by_name(updated_selected_item)
        updated_item_list = [self.get_item_by_name(item_name) for item_name in updated_item_list]
        # 构造对比内容
        content = ' || '.join([item.item_name for item in updated_item_list])
        chat_request.logger.debug(f"进行双机对比，机型列表: {content}")
        content = REFUSAL_MESSAGE_DICT[MessageType.ITEM_COMPARE][language]
        # 返回双机对比响应
        for response in self.wrap_str_to_response_stream(content, MessageType.ITEM_COMPARE, updated_selected_item,
                                                         updated_item_list):
            yield response

    async def parse_question(self, chat_request):
        # ToDo(hm): 把这个放到 parser 中
        async with TaskGroup() as tg:
            # 创建并启动所有任务
            recognize_language_task = tg.create_task(self._query_parse_service.recognize_language(chat_request))
            tag_question_first_task = tg.create_task(self._query_parse_service.tag_question_first(chat_request))
            extract_item_name_task = tg.create_task(self._query_parse_service.extract_item_names(chat_request))
            recognize_exact_task = tg.create_task(
                self._query_parse_service.recognize_item_names_for_chunk(chat_request, tg, 'exact'))
            recognize_fuzzy_task = tg.create_task(
                self._query_parse_service.recognize_item_names_for_chunk(chat_request, tg, "fuzzy"))
            second_tags_task = tg.create_task(self._query_parse_service.recognize_tags(chat_request))
            rewrite_query_task = tg.create_task(self._query_parse_service.rewrite_query(chat_request))
            language, recognize_language_task_token, recognize_language_task_start_time = await recognize_language_task
            self.record_token_usage('recognize_language', recognize_language_task_token)
            answer_intent, tag_question_first_task_token, tag_question_first_task_start_time = await tag_question_first_task
            self.record_token_usage('tag_question_first', tag_question_first_task_token)
            extracted_item_names, extract_item_names_task_token, extract_item_names_task_start_time = await extract_item_name_task
            self.record_token_usage('extracted_item_names', extract_item_names_task_token)
            second_tags, second_tags_task_token, second_tags_task_start_time = await second_tags_task
            self.record_token_usage('second_tags', second_tags_task_token)
            self.record_execution_time('second_tags', second_tags_task_start_time)
            rewritten_query, rewrite_query_task_token, rewrite_query_task_start_time = await rewrite_query_task
            self.record_token_usage('rewrite_query', rewrite_query_task_token)
            self.record_execution_time('rewrite_query', rewrite_query_task_start_time)
            chat_request.recognize_language = language
            self._second_tags = second_tags
            self._second_tags_map_dict = self._query_parse_service.get_second_tag_map_dict_from_tag_list_and_language(
                second_tags, chat_request.recognize_language.get_chinese_name())
            chat_request.logger.debug(f"识别出的二级标签：{','.join(self._second_tags)}")
            chat_request.rewritten_query = rewritten_query
            exact_item_names, fuzzy_item_names = list(), list()
            # 检查意图是否为自由问答类
            if answer_intent in IntentType.FREE_QUESTION_REJECT.as_set() or answer_intent in IntentType.FREE_QUESTION.as_set():  # 根据实际的自由问答意图类型调整
                # 取消机型识别
                await self.cancel_tasks(chat_request, [recognize_exact_task, recognize_fuzzy_task])
            else:
                # 等待机型识别任务完成
                exact_item_names, recognize_exact_task_token, recognize_exact_task_start_time = await recognize_exact_task
                self.record_token_usage('recognize_exact_task', recognize_exact_task_token)
                chat_request.logger.debug(f'exact_item_names: {exact_item_names}')
                fuzzy_item_names, recognize_fuzzy_task_token, recognize_fuzzy_task_start_time = await recognize_fuzzy_task
                self.record_token_usage('recognize_fuzzy_task', recognize_fuzzy_task_token)
                chat_request.logger.debug(f'fuzzy_item_names: {fuzzy_item_names}')
            return language, answer_intent, exact_item_names, fuzzy_item_names, extracted_item_names
